# Workflow: Refactor function gen_url_file_s3() - 29/07/2025

## M<PERSON><PERSON> tiêu
Refactor function `gen_url_file_s3()` để thay đổi cách hoạt động từ nhận trực tiếp URL S3 thành nhận CV ID và field name, đồng thời thêm logging để theo dõi truy cập file.

## Thay đổi chính

### 1. Tạo model CvAccessLog
**File:** `app/Models/CvAccessLog.php`
- Model mới để quản lý log truy cập file CV
- Các field: cv_id, field_name, user_id, user_name, ip_address, user_agent, accessed_at
- Method static `createAccessLog()` để tạo log dễ dàng
- Quan hệ với model Cv và User

### 2. Tạo function mới gen_url_file_s3_from_cv_id()
**File:** `helpers/function.php`
- Function mới: `gen_url_file_s3_from_cv_id($cv_id, $field_name, $version = true)`
- Logic:
  1. Truy vấn database lấy CV theo ID
  2. Kiểm tra field_name có tồn tại và có giá trị
  3. Ghi log truy cập với thông tin user, IP, user agent
  4. Tạo URL từ S3 path sử dụng function cũ `gen_url_file_s3()`
  5. Xử lý exception và logging lỗi
- Thêm import `use Illuminate\Support\Facades\Log;`

### 3. Cập nhật các nơi sử dụng function cũ

#### 3.1 Model Cv - Accessors
**File:** `app/Models/Cv.php`
- `getUrlCvPublicAttribute()`: Sử dụng `gen_url_file_s3_from_cv_id($this->id, 'cv_public')`
- `getUrlCvPrivateAttribute()`: Sử dụng `gen_url_file_s3_from_cv_id($this->id, 'cv_private')`

#### 3.2 DownloadController
**File:** `app/Http/Controllers/Admin/DownloadController.php`
- Method `applyJobCvPrivate()`: 
  - Ưu tiên sử dụng function mới khi có CV ID
  - Fallback về function cũ khi sử dụng metadata path
  - Thêm validation URL được tạo thành công

#### 3.3 MatchCvJob
**File:** `app/Jobs/MatchCvJob.php`
- Cập nhật sử dụng `gen_url_file_s3_from_cv_id($cv->id, 'cv_public', false)`

#### 3.4 PushToReclandFromCvProcessed Command
**File:** `app/Console/Commands/PushToReclandFromCvProcessed.php`
- Ưu tiên sử dụng function mới khi có org_cv
- Fallback về function cũ khi không có CV ID

## Các file không thay đổi (giữ nguyên function cũ)
1. `app/Services/CvService.php` - Method `hideCv()`: Nhận path trực tiếp, không có CV ID
2. `app/Http/Controllers/Admin/ApplyJobCvController.php` - Method `getCvPrivateLogs()`: Sử dụng path từ log metadata
3. Các nơi khác sử dụng function với file paths không liên quan đến CV

## Lợi ích của refactor
1. **Logging truy cập**: Theo dõi ai đã truy cập file CV nào, khi nào
2. **Bảo mật**: Kiểm soát truy cập file CV tốt hơn
3. **Audit trail**: Có thể theo dõi lịch sử truy cập file
4. **Backward compatibility**: Function cũ vẫn hoạt động cho các use case khác

## Database Migration cần thiết
- Migration `2025_07_29_000000_create_cv_access_logs_table.php` đã có sẵn

## Testing cần thực hiện
1. Test function mới với CV ID hợp lệ
2. Test với CV ID không tồn tại
3. Test với field_name không hợp lệ
4. Test logging được ghi vào database
5. Test các accessor của model CV
6. Test download CV trong DownloadController
