<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CvAccessLog extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'cv_access_logs';

    /**
     * Các trường có thể được gán hàng loạt
     */
    protected $fillable = [
        'cv_id',
        'field_name',
        'user_id',
        'user_name',
        'ip_address',
        'user_agent',
        'accessed_at',
    ];

    /**
     * Các trường sẽ được cast sang kiểu dữ liệu khác
     */
    protected $casts = [
        'accessed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Quan hệ với model Cv
     */
    public function cv()
    {
        return $this->belongsTo(Cv::class);
    }

    /**
     * Quan hệ với model User
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Tạo log truy cập CV
     */
    public static function createAccessLog($cvId, $fieldName, $userId = null, $userName = null, $ipAddress = null, $userAgent = null)
    {
        return self::create([
            'cv_id' => $cvId,
            'field_name' => $fieldName,
            'user_id' => $userId,
            'user_name' => $userName,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'accessed_at' => now(),
        ]);
    }
}
