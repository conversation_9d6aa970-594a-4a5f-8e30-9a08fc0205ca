<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Models\ApplyJob;
use App\Models\Notification;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DownloadController extends Controller
{
    public function applyJobCvPrivate(Request $request)
    {
        $apply = ApplyJob::findOrFail($request->id);

        $candidate_name = optional($apply->cv)->name;
        $job_title = $apply->job->title;

        // Kiểm tra CV có tồn tại không
        if (!$apply->cv) {
            return redirect()->back()->with('error', 'CV not found');
        }

        // Kiểm tra và ưu tiên sử dụng CV trong metadata nếu có
        $cv_private_path = $apply->getMeta('lasted_cv_private');

        if (empty($cv_private_path)) {
            // Nếu không có trong metadata, sử dụng CV private từ model Cv
            if (empty($apply->cv->cv_private)) {
                return redirect()->back()->with('error', 'CV private not found');
            }
            // Sử dụng function mới với CV ID để có logging
            $url = gen_url_file_s3_from_cv_id($apply->cv->id, 'cv_private');
        } else {
            // Nếu có trong metadata, vẫn sử dụng function cũ vì không có CV ID cụ thể
            $url = gen_url_file_s3($cv_private_path);
        }

        # return download file with name is candidate name and job title
        $file_name = 'HRI - ' . Utils::cleanFileName(Utils::shortName($candidate_name)) . ' - ' . Utils::cleanFileName($job_title) . '.pdf';

        // Kiểm tra URL có được tạo thành công không
        if (empty($url)) {
            return redirect()->back()->with('error', 'Cannot generate file URL');
        }

        // Sử dụng Guzzle để tải nội dung file
        $client = new Client();
        $response = $client->get($url);
        $content = $response->getBody()->getContents();

        // Trả về response với nội dung file để download
        return response($content)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'attachment; filename="' . $file_name . '"');
    }
}
