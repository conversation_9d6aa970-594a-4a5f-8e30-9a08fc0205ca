<?php

namespace App\Console\Commands;

use App\Models\CvProcessed;
use Illuminate\Console\Command;
use GuzzleHttp\Client;
use Illuminate\Support\Str;

class PushToReclandFromCvProcessed extends Command
{
    protected $signature = 'command:PushToReclandFromCvProcessed';
    protected $description = 'Push processed CVs to Recland';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $file = 'cv_processed_ids.txt';
        $handle = fopen($file, 'a');
        $ids = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        $chunkSize = 100;
        $processedIds = [];
        while (true) {
            CvProcessed::where('push_to_recland', 0)->where('public_path', '!=', '')->where('private_path', '!=', '')->chunk($chunkSize, function ($cvs) use ($handle, $ids, &$processedIds) {
                foreach ($cvs as $cv) {
                    $client = new Client();

                    if (in_array($cv->id, $processedIds)) {
                        $this->info($cv->id . ' Đã xử lý');
                        continue;
                    } else {
                        $this->info($cv->id . ' Bắt đầu xử lý');
                    }

                    $raw_data = str_replace("'", '"', $cv->raw_data);
                    $raw_data = json_decode($raw_data, true);
                    // dd($raw_data);
                    $org_cv = $cv->cv;

                    // Xử lý salary_expect mặc định (có thể điều chỉnh theo yêu cầu)
                    // $salary_expect = 1000; // USD
                    // $currency = 'USD';
                    $salary_expect = $org_cv->salary_expect ?? 0; // USD
                    $currency = $org_cv->currency ?? 'VND';

                    $url = 'https://recland.co/api/selling-cv/create';
                    // $url = 'http://recland.local/api/selling-cv/create';

                    // Sử dụng CV ID để tạo URL và có logging
                    if ($org_cv) {
                        $public_path = strpos($cv->public_path, 'https://') !== false ?
                            $cv->public_path : gen_url_file_s3_from_cv_id($org_cv->id, 'cv_public', false);

                        $private_path = strpos($cv->private_path, 'https://') !== false ?
                            $cv->private_path : gen_url_file_s3_from_cv_id($org_cv->id, 'cv_private', false);
                    } else {
                        // Fallback nếu không có org_cv
                        $public_path = strpos($cv->public_path, 'https://') !== false ?
                            $cv->public_path : gen_url_file_s3($cv->public_path, '', false);

                        $private_path = strpos($cv->private_path, 'https://') !== false ?
                            $cv->private_path : gen_url_file_s3($cv->private_path, '', false);
                    }

                    $skills = data_get($raw_data, 'skills', []);
                    if (empty($skills)) {
                        $skills = $org_cv->skills()->pluck('name')->toArray();
                    }
                    if (!is_array($skills)) {
                        $skills = explode(',', $skills);
                    }
                    $skills += explode(',', $org_cv->skill_describe);
                    $skills = array_unique($skills);
                    $address = data_get($raw_data, 'address', $org_cv->address);
                    if (is_array($address)) {
                        $address = implode(',', $address);
                    }
                    $slug_address = Str::slug($address);
                    if (strpos($slug_address, 'ha-noi') !== false || strpos($slug_address, 'hanoi') !== false || strpos($slug_address, 'hn') !== false) {
                        $slug_address = 'ha-noi';
                    } elseif (strpos($slug_address, 'ho-chi-minh') !== false || strpos($slug_address, 'hcm') !== false || strpos($slug_address, 'hcmc') !== false) {
                        $slug_address = 'ho-chi-minh';
                    } else {
                        $slug_address = 'other';
                    }
                    // dd($raw_data);
                    // dd(data_get($raw_data, 'job_title'));

                    $data = array(
                        'private_cv_upload'          => '',
                        'cv_public'                  => $public_path,
                        'cv_private'                 => $private_path,
                        'id'                         => 0,
                        'candidate_name'             => data_get($raw_data, 'full_name', $org_cv->name),
                        'candidate_mobile'           => data_get($raw_data, 'phone_number', $org_cv->mobile),
                        'candidate_email'            => data_get($raw_data, 'email', $org_cv->email),
                        'year_experience'            => data_get($raw_data, 'years_experience', $org_cv->yoe),
                        'candidate_job_title'        => data_get($raw_data, 'job_title', $org_cv->job_title),
                        'rank'                       => '',
                        'candidate_portfolio'        => '',
                        'candidate_salary_expect'    => intval($salary_expect),
                        'candidate_salary_expect_to' => intval($salary_expect),
                        'candidate_currency'         => $currency,
                        'career'                     => [30, 31],
                        'assessment'                 => '',
                        'candidate_location'         => $slug_address,
                        'skills'                     => $skills,
                        'candidate_formwork'         => 1,
                        'candidate_est_timetowork'   => 2,
                        'selling_skill'              => '',
                        'is_authority'               => 1,
                        'type_of_sale'               => 'cv',
                        'source'                     => 'crm.hri.com.vn',
                        'created_at'                 => $org_cv->created_at ? date('Y-m-d H:i:s', strtotime($org_cv->created_at)) : '',
                        'updated_at'                 => $org_cv->updated_at ? date('Y-m-d H:i:s', strtotime($org_cv->updated_at)) : '',
                    );

                    // dd($data);
                    try {
                        $response = $client->post($url, [
                            'json' => $data,
                            'http_errors' => false
                        ]);

                        $response_body = $response->getBody()->getContents();
                        $result = json_decode($response_body, true);

                        if (!$result) {
                            $this->error("Không thể parse JSON response cho CV ID: " . $cv->id);
                            continue;
                        }

                        $id = data_get($result, 'data.id');
                        $this->info($cv->id . ' ' . ($raw_data['email'] ?? 'N/A') . ' ' . ($id ? $id : 'Lỗi'));

                        if ($id) {
                            $cv->update(['push_to_recland' => 1]);
                        } else {
                            $cv->update(['push_to_recland' => -1]);
                        }
                        $this->info('Đã gửi CV ID: ' . $cv->id);
                    } catch (\Exception $e) {
                        $cv->update(['push_to_recland' => -1]);
                        $this->error("Lỗi khi gửi CV ID {$cv->id}: " . $e->getMessage());
                        continue;
                    }
                }
            });
        }
        return Command::SUCCESS;
    }
}
