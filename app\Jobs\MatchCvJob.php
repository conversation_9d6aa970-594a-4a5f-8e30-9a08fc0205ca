<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Jobs\Middleware\WithoutOverlapping;
use App\Models\Job;
use App\Models\MatchScoreCv;
use App\Models\Cv;
use App\Models\ApplyJob;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use League\HTMLToMarkdown\HtmlConverter;
use App\Services\Frontend\JobService;

class MatchCvJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private const API_URL = 'https://n8n.hri.com.vn/webhook/match-score-cv';
    // private const API_URL = 'https://n8n.hri.com.vn/webhook-test/match-score-cv';
    private const API_KEY = 'hri@1008';

    private Cv|ApplyJob $cvOrApply;
    private Job $jobModel;

    public function __construct(
        Cv|ApplyJob $cvOrApply,
        Job $jobModel,
    ) {
        $this->cvOrApply = $cvOrApply;
        $this->jobModel = $jobModel;
    }



    public function handle(): void
    {
        // Lấy CV từ đối tượng truyền vào
        if ($this->cvOrApply instanceof Cv) {
            $cv = $this->cvOrApply;
        } else {
            // ApplyJob case
            $cv = $this->cvOrApply->cv;
        }

        // Lấy URL của CV public
        $cvUrl = $cv->cv_public;
        if (!$cvUrl) {
            Log::warning('CV không có file public', ['cv_id' => $cv->id]);
            return;
        }

        $cvUrl = gen_url_file_s3_from_cv_id($cv->id, 'cv_public', false);
        $cvUrl = str_replace('recland-dev', 'recland', $cvUrl);

        // Tạo job description từ job model
        $jobDescription = $this->getJobDescription($this->jobModel);

        try {
            $response = Http::withHeaders([
                'content-type' => 'application/json',
                'match-cv-api-key' => self::API_KEY,
            ])->post(self::API_URL, [
                'job_description' => $jobDescription,
                'cv_url' => $cvUrl,
            ]);

            if (!$response->successful()) {
                Log::error('CV matching API failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
                return;
            }

            $data = $response->json();
            if (empty($data)) {
                return;
            }
            if (!isset($data['overview']['score'])) {
                return;
            }

            $matchScore = MatchScoreCv::where([
                'parent_type' => get_class($this->cvOrApply),
                'parent_id' => $this->cvOrApply->id,
                'job_id' => $this->jobModel->id,
                'cv_id' => $cv->id,
            ])->first();

            $scoreData = [
                'parent_type' => get_class($this->cvOrApply),
                'parent_id' => $this->cvOrApply->id,
                'job_id' => $this->jobModel->id,
                'cv_id' => $cv->id,
                'experience_score' => $data['experience']['score'] ?? 0,
                'skills_score' => $data['skills']['score'] ?? 0,
                'overview_score' => $data['overview']['score'] ?? 0,
                'raw_data' => $data,
            ];

            if ($matchScore) {
                $matchScore->update($scoreData);
            } else {
                $matchScore = MatchScoreCv::create($scoreData);
            }
            // dd($matchScore);
        } catch (\Exception $e) {
            echo $e->getMessage();
            Log::error('CV matching failed', [
                'error' => $e->getMessage(),
                'cv_id' => $cv->id,
                'job_id' => $this->jobModel->id,
            ]);
        }
    }

    /**
     * Tạo job description từ Job model
     */
    private function getJobDescription(Job $job): string
    {
        $converter = new HtmlConverter();
        $description = [];

        // Thêm tên job
        if ($job->job_name) {
            $description[] = "Tên công việc: " . $converter->convert($job->job_name);
        }

        // Thêm mô tả
        if ($job->description) {
            $description[] = "Mô tả: " . $converter->convert($job->description);
        }

        // Thêm yêu cầu
        if ($job->requirement) {
            $description[] = "Yêu cầu: " . $converter->convert($job->requirement);
        }

        // Thêm lợi ích
        if ($job->benefit) {
            $description[] = "Lợi ích: " . $converter->convert($job->benefit);
        }

        // Thêm kỹ năng
        if ($job->skills && $job->skills->count() > 0) {
            $skills = $job->skills->pluck('name')->implode(', ');
            $description[] = "Kỹ năng yêu cầu: " . $skills;
        }

        // Thêm cấp độ
        if ($job->levels && $job->levels->count() > 0) {
            $levels = $job->levels->pluck('name_vi')->implode(', ');
            $description[] = "Cấp độ: " . $levels;
        }

        return implode("\n\n", $description);
    }
}
