<?php

namespace App\Models;

use App\Jobs\PushCvToRecland;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use Illuminate\Database\Eloquent\SoftDeletes;

class Cv extends Model implements AuditableContract
{
    use CrudTrait, HasFactory, Auditable, SoftDeletes;


    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'cvs';

    protected $guarded = ['id'];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->created_by)) {
                $model->created_by = backpack_auth()->id();
            }
        });
        static::saved(function ($model) {
            if ($model->pushed_to_recland == 0) {
                PushCvToRecland::dispatch($model);
            }
        });
        static::saving(function ($model) {
            if ($model->cv_private == '0') {
                $model->cv_private = null;
            }
            if (empty($model->email)) {
                $candaite = Candidate::find($model->candidate_id);
                if ($candaite) {
                    $model->email = $candaite->email;
                }
            }
            if (empty($model->mobile)) {
                if (!isset($candaite) || !$candaite) {
                    $candaite = Candidate::find($model->candidate_id);
                }
                $model->mobile = $candaite->mobile;
            }
            if (empty($model->university)) {
                if (!isset($candaite) || !$candaite) {
                    $candaite = Candidate::find($model->candidate_id);
                }
                $model->university = $candaite->university;
            }
            if (empty($model->address)) {
                if (!isset($candaite) || !$candaite) {
                    $candaite = Candidate::find($model->candidate_id);
                }
                $model->address = $candaite->address;
            }
            if (empty($model->facebook)) {
                if (!isset($candaite) || !$candaite) {
                    $candaite = Candidate::find($model->candidate_id);
                }
                $model->facebook = $candaite->facebook;
            }
            if (empty($model->linkedin)) {
                if (!isset($candaite) || !$candaite) {
                    $candaite = Candidate::find($model->candidate_id);
                }
                $model->linkedin = $candaite->linkedin;
            }
        });
    }
    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    public function getUrlCvPublicAttribute()
    {
        return gen_url_file_s3_from_cv_id($this->id, 'cv_public');
    }

    public function getUrlCvPrivateAttribute()
    {
        return gen_url_file_s3_from_cv_id($this->id, 'cv_private');
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function careerLanguages()
    {
        return $this->belongsToMany(CareerLanguage::class, 'cv_career_languages', 'cv_id',);
    }

    public function skills()
    {
        return $this->belongsToMany(Skill::class, 'cv_skills', 'cv_id');
    }

    public function careerLevel()
    {
        return $this->belongsTo(CareerLevel::class, 'level');
    }

    public function candidate()
    {
        return $this->belongsTo(Candidate::class);
    }

    public function academicLevel()
    {
        return $this->belongsTo(AcademicLevel::class, 'academic_level');
    }

    public function applies()
    {
        return $this->hasMany(ApplyJob::class, 'cv_id');
    }
    public function raw_data(): MorphOne
    {
        return $this->morphOne(RawData::class, 'object');
    }
    public function attachment(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'object');
    }
    public function comment(): MorphMany
    {
        return $this->morphMany(Comment::class, 'object');
    }

    public function statusGender()
    {
        return $this->belongsTo(Status::class, 'gender');
    }

    public function statusWorkSite()
    {
        return $this->belongsTo(Status::class, 'work_site');
    }
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function matchScores()
    {
        return $this->morphMany(MatchScoreCv::class, 'parent');
    }
    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */
    public function scopeRoleData($query)
    {
        // return $query;
        return $query->where(function ($q) {
            $user = backpack_user();
            if ($user->can('cv.all-data')) {
                return $q;
            } else {
                $userIds = [$user->id];
                if ($user->can('cv.only-company')) {
                    $userIds = User::where('internal_company_id', $user->internal_company_id)
                        ->pluck('id')->toArray();
                } elseif ($user->can('cv.only-team')) {
                    $userIds = User::where('department_id', $user->department_id)
                        ->pluck('id')->toArray();
                }
                return $q->where('candidates.id', $user->id)
                    ->orWhereIn('cvs.candidate_id', $userIds);
            }
        });
    }

    public function scopeOutSide($query)
    {
        $user = backpack_user();
        // dd($user->canOutsideAccess());
        if (!$user->canOutsideAccess()) {
            return $query->where('created_by', $user->id);
        }
        return $query;
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
}
